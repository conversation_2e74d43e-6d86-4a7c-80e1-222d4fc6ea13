// ignore_for_file: unused_element, deprecated_member_use

import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/core/utils/routes_observer.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/profile_screen/presentation/page/follow/user_follow_id_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/following/user_following_id_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/widget/profile_shimmer.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/widgets/common/app_bottom_sheet.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_post_by_id_widget.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_video_by_id_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';

class GetUserProfileById extends StatefulWidget {
  final int? userId;
  final bool? stackonScreen;
  const GetUserProfileById({super.key, this.userId, this.stackonScreen});
  static Widget builder(BuildContext context) {
    return GetUserProfileById();
  }

  @override
  State<GetUserProfileById> createState() => _GetUserProfileByIdState();
}

class _GetUserProfileByIdState extends State<GetUserProfileById> with SingleTickerProviderStateMixin, RouteAware {
  late TabController _tabController;
  double _lastOffset = 0;
  // ignore: unused_field
  bool _showAppBar = true;
  // ignore: unused_field
  bool _showHorizontalList = true;
  // ignore: unused_field
  bool _isLoadingMore = false;

  late ScrollController _scrollController;

  // Offline message state
  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;
  late bool isBlockPermission;

  @override
  void initState() {
    super.initState();
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
    final state = context.read<HomeFeedBloc>().state;
    state.isuserProfileposts.clear();
    state.getProfileByIDvideo.clear();
    state.getUserByIdTextPostData.clear();
    context.read<HomeFeedBloc>().add(GetUserProfilebyIdApi(userId: widget.userId ?? 0));
    context.read<HomeFeedBloc>().add(GetUserPostApiIdEvent(page: 1, userId: widget.userId));
    context.read<HomeFeedBloc>().add(GetUserVideoByIdApiEvent(page: 1, userId: widget.userId));
    context.read<HomeFeedBloc>().add(GetUserByIdTextPostApiIdEvent(page: 1, userId: widget.userId));
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();
  }

  // void _scrollListener() {
  //   _handlePagination();
  //   _handleHorizontalListVisibility();
  // }
  @override
  void didUpdateWidget(covariant GetUserProfileById old) {
    super.didUpdateWidget(old);
    if (old.userId != widget.userId) _refreshFeed();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context)! as PageRoute);
  }

  void _handleHorizontalListVisibility() {
    double currentOffset = _scrollController.offset;
    bool isScrollingUp = currentOffset < _lastOffset;
    double itemHeight = 116.h;
    int currentIndex = (currentOffset / itemHeight).floor();

    setState(() {
      if (currentIndex == 0 || currentIndex == 1 && currentOffset > 0) {
        _showHorizontalList = true;
      } else if (currentIndex >= 1) {
        _showHorizontalList = false;
      }
      if (currentOffset <= 0 || isScrollingUp) {
        _showAppBar = true;
      } else {
        _showAppBar = false;
      }
    });
    _lastOffset = currentOffset;
  }

  @override
  void didPopNext() {
    super.didPopNext();
    final state = context.read<HomeFeedBloc>().state;

    if (state.userProfile?.data.id != widget.userId) {
      _refreshFeed(); // fetch only when different profile
    } // This refetches the data
  }

  // void _handlePagination() {
  //   if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
  //     final state = context.read<HomeFeedBloc>().state;
  //     if (state.getAllUserPostbyidModel?.nextPage == null) {
  //       return;
  //     } else {
  //       if (!state.profilPosteByIdLoadingmore) {
  //         context
  //             .read<HomeFeedBloc>()
  //             .add(GetUserPostApiIdEvent(page: state.userpostbyIdPage + 1, userId: widget.userId));
  //       }
  //     }
  //     if (!state.getProfileisVideoByIDLoadingMore) {
  //       context.read<HomeFeedBloc>().add(GetUserVideoByIdApiEvent(page: state.videoPage + 1, userId: widget.userId));
  //     }
  //     if (!state.getUserByIdTextPostLoadingMore) {
  //       context
  //           .read<HomeFeedBloc>()
  //           .add(GetUserByIdTextPostApiIdEvent(page: state.getUserByIdTextPostPage + 1, userId: widget.userId));
  //     }
  //   }
  // }

  Future<void> _refreshFeed() async {
    final state = context.read<HomeFeedBloc>().state;
    final connectivityState = context.read<ConnectivityBloc>().state;
    if (state.isuserProfileposts.isNotEmpty) {
      state.isuserProfileposts.clear();
    }
    if (state.getProfileByIDvideo.isNotEmpty) {
      state.getProfileByIDvideo.clear();
    }
    if (state.getUserByIdTextPostData.isNotEmpty) {
      state.getUserByIdTextPostData.clear();
    }
    _isLoadingMore = false;

    if (!connectivityState.isConnected) {
      // User attempted to refresh while offline
      _isRefreshAttemptedOffline = true;
      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 5), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });
      return;
    }
    context.read<HomeFeedBloc>().add(GetUserProfilebyIdApi(userId: widget.userId ?? 0));
    context.read<HomeFeedBloc>().add(GetUserPostApiIdEvent(page: 1, userId: widget.userId));
    context.read<HomeFeedBloc>().add(GetUserVideoByIdApiEvent(page: 1, userId: widget.userId));
    context.read<HomeFeedBloc>().add(GetUserByIdTextPostApiIdEvent(page: 1, userId: widget.userId));
    context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
  }

  @override
  void dispose() {
    _tabController.dispose();
    routeObserver.unsubscribe(this);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    isBlockPermission = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    return Stack(
      alignment: Alignment.center,
      children: [
        Scaffold(
          body: BlocListener<ConnectivityBloc, ConnectivityState>(
            listener: (context, state) {
              if (state.isReconnected) {
                if (context.read<HomeFeedBloc>().state.userProfile?.data == null) {
                  _refreshFeed();
                }
              }
            },
            child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
              builder: (context, state) {
                return BlocBuilder<ConnectivityBloc, ConnectivityState>(
                  builder: (context, connectivityState) {
                    if (!connectivityState.isConnected && state.userProfile?.data == null) {
                      return ProfileShimmer(isCurrentUser: false);
                    } else if (state.profileByIdLoading) {
                      return ProfileShimmer(isCurrentUser: false);
                    } else if (state.userProfile?.data == null) {
                      return ProfileShimmer(isCurrentUser: false);
                    } else {
                      return Scaffold(
                        appBar: _buildAppBar(state),
                        body: DefaultTabController(
                          length: 3,
                          child: SafeArea(
                            bottom: false,
                            child: LiquidPullToRefresh(
                              color: Theme.of(context).primaryColor.withOpacity(0.5),
                              showChildOpacityTransition: false,
                              backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                              onRefresh: _refreshFeed,
                              child: Stack(
                                children: [
                                  Column(
                                    children: [
                                      Expanded(
                                        child: NestedScrollView(
                                          headerSliverBuilder: (context, _) {
                                            return [
                                              SliverList(
                                                delegate: SliverChildListDelegate(
                                                  [
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: 12.0.w),
                                                      child: Column(
                                                        children: [
                                                          _buildProfileInfo(state, context),
                                                          buildSizedBoxH(16.h),
                                                          AnimatedContainer(
                                                              duration: const Duration(milliseconds: 300),
                                                              child: state.userProfile?.data.isBlocked == true
                                                                  ? Padding(
                                                                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                                                                      child: _buildUnBlockedbutton(),
                                                                    )
                                                                  : state.userProfile?.data.id.toString() ==
                                                                          Prefobj.preferences?.get(Prefkeys.USER_ID)
                                                                      ? SizedBox.shrink()
                                                                      : Padding(
                                                                          padding:
                                                                              EdgeInsets.symmetric(horizontal: 16.w),
                                                                          child: _buildFollowandMessageBtn(state),
                                                                        )),
                                                          buildSizedBoxH(20),
                                                        ],
                                                      ),
                                                    ),
                                                    // _buildhighlightStory(state: state),
                                                  ],
                                                ),
                                              ),
                                            ];
                                          },
                                          body: Column(
                                            children: <Widget>[
                                              Material(color: Colors.transparent, child: _buildTabBar()),
                                              Expanded(
                                                child: TabBarView(
                                                  physics: const NeverScrollableScrollPhysics(),
                                                  controller: _tabController,
                                                  children: [
                                                    _buildPostContentGrid(state),
                                                    _buildVideoContent(state),
                                                    _buildTextPostContent(state),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  BlocBuilder<HomeFeedBloc, HomeFeedState>(
                                    builder: (context, homeFeedState) {
                                      return Align(
                                        alignment: Alignment.bottomCenter,
                                        child: Visibility(
                                          visible: homeFeedState.profilPosteByIdLoadingmore ||
                                              homeFeedState.getProfileisVideoByIDLoadingMore ||
                                              homeFeedState.getUserByIdTextPostLoadingMore,
                                          child: SizedBox(
                                              height: 50.h,
                                              child: Center(
                                                  child: CupertinoActivityIndicator(
                                                      color: Theme.of(context).colorScheme.primary))),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    }
                  },
                );
              },
            ),
          ),
        ),
        _buildOfflineMessage(_showOfflineMessage),
      ],
    );
  }

  PreferredSizeWidget _buildAppBar(HomeFeedState state) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
            Navigator.pop(context);
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          state.userProfile?.data.username ?? "",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
      actions: widget.userId == Prefobj.preferences?.get(Prefkeys.USER_ID) || state.userProfile?.data.isBlocked == true
          ? null
          : [
              CustomImageView(
                onTap: isBlockPermission
                    ? () {
                        FocusScope.of(context).unfocus();
                        VibrationHelper.singleShortBuzz();
                        _showBlockBottomSheet(context);
                      }
                    : () {
                        showToastNoPermission(access: "block user");
                      },
                imagePath: AssetConstants.icMore,
                color: Theme.of(context).iconTheme.color,
                width: 18.w,
                height: 18.h,
              ),
              buildSizedBoxW(12.0),
            ],
    );
  }

  Widget _buildFollowandMessageBtn(HomeFeedState state) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () {
              setState(() {});
              context.read<HomeFeedBloc>().add(FollowUserSocketEvent(userId: int.parse(widget.userId.toString())));
            },
            child: (state.userProfile?.data.isFollowing ?? false)
                ? Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).primaryColor),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      child: Text(
                        Lang.of(context).lbl_following,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                : Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      border: Border.all(color: Theme.of(context).primaryColor),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      child: Text(
                        Lang.of(context).lbl_follow,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
          ),
        ),
        buildSizedBoxW(10.w),
        Expanded(
          child: InkWell(
            onTap: () {
              setState(() {});

              // context.read<HomeFeedBloc>().add(FollowUserSocketEvent(userId: int.parse(widget.userId.toString())));
            },
            child: (state.userProfile?.data.isFollowing ?? false)
                ? Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).primaryColor),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      child: Text(
                        Lang.of(context).lbl_following,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                : Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      border: Border.all(color: Theme.of(context).primaryColor),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      child: Text(
                        Lang.of(context).lbl_follow,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileInfo(HomeFeedState state, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              height: 89.h,
              width: 89.w,
              padding: EdgeInsets.all(state.userProfile?.data.profileImage == '' ? 16 : 0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100.r),
                border: Border.all(
                  color: state.userProfile?.data.profileImage == ''
                      ? Theme.of(context).primaryColor.withOpacity(0.2)
                      : Theme.of(context).primaryColor.withOpacity(0.05),
                  width: 2.w,
                ),
              ),
              child: CustomImageView(
                imagePath: state.userProfile?.data.profileImage == ''
                    ? AssetConstants.pngUser
                    : state.userProfile?.data.profileImage,
                radius: BorderRadius.circular(state.userProfile?.data.profileImage == '' ? 0 : 100.r),
                fit: state.userProfile?.data.profileImage == '' ? BoxFit.contain : BoxFit.cover,
                fallbackImage: AssetConstants.pngUserReomve,
              ),
            ),
            buildSizedBoxW(16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(state.userProfile?.data.name ?? "",
                      overflow: TextOverflow.ellipsis,
                      style:
                          Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp, color: Color(0xff22240F))),
                  buildSizedBoxH(6.h),
                  Container(
                    height: 60.h,
                    decoration: BoxDecoration(
                      color: Theme.of(context).customColors.greyContainerBg.withOpacity(0.9),
                      border: Border.all(color: Color.fromARGB(84, 99, 97, 84), width: 0.5.w),
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          userStatItem(
                            value: state.userProfile?.data.numberOfPost ?? 0,
                            label: Lang.of(context).lbl_post,
                          ),
                          verticalDivider(),
                          userStatItem(
                            value: state.userProfile?.data.numberOfFollowers ?? 0,
                            label: Lang.of(context).lbl_followers,
                            onTap: () {
                              PersistentNavBarNavigator.pushNewScreen(context,
                                  screen: UserFollowbyIdScreen(userId: widget.userId));
                            },
                          ),
                          verticalDivider(),
                          userStatItem(
                            value: state.userProfile?.data.numberOfFollowing ?? 0,
                            label: Lang.of(context).lbl_following,
                            onTap: () {
                              PersistentNavBarNavigator.pushNewScreen(context,
                                  screen: UserFollowingbyIdScreen(userId: widget.userId));
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        if (state.userProfile?.data.bio.isNotEmpty ?? false)
          Column(
            children: [
              buildSizedBoxH(10.h),
              BioTextWidget(text: state.userProfile?.data.bio ?? ""),
            ],
          ),
      ],
    );
  }

  Widget userStatItem({required int value, required String label, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        width: label == Lang.of(context).lbl_post ? 40.w : 55.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              abbreviateNumber(value),
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontSize: 15.sp, fontWeight: FontWeight.bold, color: Color(0xff563D39)),
            ),
            buildSizedBoxH(4.h),
            Text(
              label,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontSize: 12.sp, fontWeight: FontWeight.w500, color: Color(0xff8D8480)),
            ),
          ],
        ),
      ),
    );
  }

  Widget verticalDivider() {
    return Container(
      height: 40,
      width: 0.8,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0),
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 48.h,
      margin: EdgeInsets.symmetric(vertical: 4.h, horizontal: 13.w),
      padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.greyContainerBg,
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: TabBar(
        onTap: (value) {
          setState(() {
            _tabController.index = value;
          });
        },
        controller: _tabController,
        labelColor: Theme.of(context).customColors.white,
        unselectedLabelColor: Theme.of(context).customColors.black,
        dividerColor: Theme.of(context).customColors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: WidgetStateColor.transparent,
        indicator: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(25.r),
        ),
        padding: EdgeInsets.zero,
        labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 14.sp),
        unselectedLabelStyle:
            Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 14.sp),
        textScaler: TextScaler.linear(0.93),
        tabs: [
          Tab(
              child: CustomImageView(
            imagePath: Assets.images.svg.profile.svgPost.path,
            color: (_tabController.index == 0)
                ? Theme.of(context).customColors.white
                : Theme.of(context).customColors.black,
          )),
          Tab(
              child: CustomImageView(
            imagePath: Assets.images.svg.profile.svgVideo.path,
            color: (_tabController.index == 1)
                ? Theme.of(context).customColors.white
                : Theme.of(context).customColors.black,
          )),
          Tab(
            child: CustomImageView(
              imagePath: Assets.images.svg.profile.svgTextPost.path,
              color: (_tabController.index == 2)
                  ? Theme.of(context).customColors.white
                  : Theme.of(context).customColors.black,
              height: 20.h,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostContentGrid(HomeFeedState state) {
    // if (state.userProfile?.data.posts.isEmpty ?? true) {
    //   return SingleChildScrollView(
    //     child: Column(
    //       children: [
    //         buildSizedBoxH(20.0),
    //         ExceptionWidget(
    //           imagePath: Assets.images.pngs.pngNoPost.path,
    //           title: Lang.of(context).lbl_no_post,
    //           subtitle: '',
    //           showButton: false,
    //         ),
    //       ],
    //     ),
    //   );
    // }

    return state.profilPosteByIdLoading
        ? buildShimmerGrid()
        : state.isuserProfileposts.isEmpty
            ? Column(
                children: [
                  buildSizedBoxH(20.0),
                  ExceptionWidget(
                    imagePath: Assets.images.pngs.pngNoPost.path,
                    title: Lang.of(context).lbl_no_post,
                    subtitle: '',
                    showButton: false,
                  ),
                ],
              )
            : NotificationListener<ScrollUpdateNotification>(
                onNotification: (notification) {
                  if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
                    // _handlePagination();
                    // Handle video pagination
                    if (state.getAllUserPostbyidModel?.nextPage != null && !state.profilPosteByIdLoadingmore) {
                      _isLoadingMore = true;
                      context
                          .read<HomeFeedBloc>()
                          .add(GetUserPostApiIdEvent(page: state.userpostbyIdPage + 1, userId: widget.userId));
                    }
                  }
                  return true;
                },
                child: StaggeredGridView.countBuilder(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  shrinkWrap: true,
                  crossAxisCount: 2,
                  mainAxisSpacing: 2.0,
                  crossAxisSpacing: 2.0,
                  staggeredTileBuilder: (int index) {
                    return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                  },
                  itemCount: state.isuserProfileposts.length,
                  itemBuilder: (context, index) {
                    final filePath = state.isuserProfileposts[index].files.isNotEmpty
                        ? state.isuserProfileposts[index].files.first
                        : Assets.images.pngs.other.pngPlaceholder.path;

                    return InkWell(
                      onTap: () {
                        FocusScope.of(context).unfocus();
                        PersistentNavBarNavigator.pushNewScreen(
                          context,
                          screen: GetUserPostByIdWidget(
                            initialIndex: index,
                            userByIdPost: state.isuserProfileposts,
                            userId: widget.userId,
                          ),
                        );
                      },
                      child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(18.0.r),
                            border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                        child: isVideo(filePath)
                            ? state.isuserProfileposts[index].thumbnailFiles!.isNotEmpty
                                ? GestureDetector(
                                    child: Stack(
                                      fit: StackFit.expand,
                                      children: [
                                        CustomImageView(
                                          width: double.infinity,
                                          imagePath: state.isuserProfileposts[index].thumbnailFiles?.first,
                                          fit: BoxFit.cover,
                                          radius: BorderRadius.circular(16.0.r),
                                        ),
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            CustomImageView(
                                              margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                              imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                              color: Colors.white70,
                                              height: 30.h,
                                              width: 30.w,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  )
                                : Container(
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade300,
                                      image: DecorationImage(
                                        image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                      ),
                                      borderRadius: BorderRadius.circular(16.0.r),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                          child: CustomImageView(
                                            // margin: EdgeInsets.symmetric(horizontal: 50.w, vertical: 50.h),
                                            height: 30.h,
                                            width: 30.w,
                                            imagePath: Assets.images.svg.other.icPlayVideo.path,
                                            // fit: BoxFit.contain,
                                            radius: BorderRadius.circular(16.0.r),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                            : CustomImageView(
                                imagePath: filePath,
                                fit: BoxFit.cover,
                                radius: BorderRadius.circular(16.0.r),
                              ),
                      ),
                    );
                  },
                ),
              );
  }

  Widget _buildVideoContent(HomeFeedState state) {
    final filteredVideos = state.getProfileByIDvideo.where((video) => video.user.userId == widget.userId).toList();

    return state.getProfileVideoByIDLoading
        ? buildShimmerGrid()
        : filteredVideos.isEmpty
            ? Column(
                children: [
                  buildSizedBoxH(20.0),
                  ExceptionWidget(
                    imagePath: Assets.images.pngs.pngNoPost.path,
                    title: "No Video Post",
                    subtitle: "",
                    showButton: false,
                  ),
                ],
              )
            : NotificationListener<ScrollUpdateNotification>(
                onNotification: (notification) {
                  if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
                    // _handlePagination();
                    // Handle video pagination
                    if (state.getProfilevideoByIDResponseModel?.next != null &&
                        !state.getProfileisVideoByIDLoadingMore) {
                      _isLoadingMore = true;
                      context.read<HomeFeedBloc>().add(
                          GetUserVideoByIdApiEvent(page: state.getProfilevideoByIDPage + 1, userId: widget.userId));
                    }
                  }
                  return true;
                },
                child: StaggeredGridView.countBuilder(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  shrinkWrap: true,
                  crossAxisCount: 2,
                  mainAxisSpacing: 2.0,
                  crossAxisSpacing: 2.0,
                  staggeredTileBuilder: (int index) {
                    return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                  },
                  itemCount: filteredVideos.length,
                  itemBuilder: (context, index) {
                    final filePath = filteredVideos[index].files.isNotEmpty
                        ? filteredVideos[index].files.first
                        : Assets.images.pngs.other.pngPlaceholder.path;

                    return Container(
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18.0.r),
                          border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                      child: isVideo(filePath)
                          ? filteredVideos[index].thumbnailFiles.isNotEmpty
                              ? GestureDetector(
                                  onTap: () {
                                    FocusManager.instance.primaryFocus?.unfocus();

                                    try {
                                      final selectedPost = filteredVideos[index];

                                      List<PostData> convertedPosts = filteredVideos
                                          .where((video) => video.files.any((file) => isVideo(file)))
                                          .map((video) => PostData(
                                                id: video.id,
                                                title: video.title,
                                                description: video.description,
                                                location: video.location,
                                                likes: video.likes,
                                                dislikes: video.dislikes,
                                                commentsCount: video.commentsCount,
                                                createdAt: video.createdAt,
                                                files: video.files,
                                                thumbnailFiles: video.thumbnailFiles,
                                                latestComment: video.latestComment,
                                                user: User.fromJson(video.user.toJson()),
                                                isLiked: video.isLiked,
                                                isSaved: video.isSaved,
                                                width: video.width,
                                                height: video.height,
                                                isTextPost: false,
                                              ))
                                          .toList();

                                      // Find correct index in filtered list
                                      final matchedIndex =
                                          convertedPosts.indexWhere((post) => post.id == selectedPost.id);

                                      PersistentNavBarNavigator.pushNewScreen(
                                        context,
                                        screen: VideoReelPage(
                                          index: matchedIndex >= 0 ? matchedIndex : 0,
                                          userId: selectedPost.user.userId,
                                          reelService: ReelService(),
                                          screen: 'Follower',
                                          postDataList: convertedPosts,
                                        ),
                                        withNavBar: false,
                                      );
                                    } catch (e, stack) {
                                      Logger.lOG("Error: $e\n$stack");
                                    }
                                    Logger.lOG("widget.postdata Before ${filteredVideos[index].id}");
                                  },
                                  child: Stack(
                                    fit: StackFit.expand,
                                    children: [
                                      CustomImageView(
                                        width: double.infinity,
                                        imagePath: filteredVideos[index].thumbnailFiles.first,
                                        fit: BoxFit.cover,
                                        radius: BorderRadius.circular(16.0.r),
                                      ),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          CustomImageView(
                                            margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                            imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                            color: Colors.white70,
                                            height: 30.h,
                                            width: 30.w,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade300,
                                    image: DecorationImage(
                                      image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                    ),
                                    borderRadius: BorderRadius.circular(16.0.r),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                        child: CustomImageView(
                                          // margin: EdgeInsets.symmetric(horizontal: 50.w, vertical: 50.h),
                                          height: 30.h,
                                          width: 30.w,
                                          imagePath: Assets.images.svg.other.icPlayVideo.path,
                                          // fit: BoxFit.contain,
                                          radius: BorderRadius.circular(16.0.r),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                          : GestureDetector(
                              onTap: () {
                                FocusScope.of(context).unfocus();
                                // Find the correct index in the original list for navigation
                                final originalIndex = state.getProfileByIDvideo
                                    .indexWhere((video) => video.id == filteredVideos[index].id);
                                PersistentNavBarNavigator.pushNewScreen(
                                  context,
                                  screen: GetUserVideoByIdWidget(
                                    initialIndex: originalIndex >= 0 ? originalIndex : index,
                                    userByIdVideo: filteredVideos,
                                    userId: widget.userId,
                                  ),
                                );
                              },
                              child: CustomImageView(
                                imagePath: filePath,
                                fit: BoxFit.cover,
                                radius: BorderRadius.circular(16.0.r),
                              ),
                            ),
                    );
                  },
                ),
              );
  }

  Widget _buildTextPostContent(HomeFeedState state) {
    if (state.getUserByIdTextPostData.isEmpty) {
      return Column(
        children: [
          buildSizedBoxH(20.0),
          ExceptionWidget(
            imagePath: Assets.images.pngs.pngNoPost.path,
            title: "No Text Post Found",
            subtitle: "",
            showButton: false,
          ),
        ],
      );
    }

    return state.getUserByIdTextPostLoading
        ? Center(
            child: LoadingAnimationWidget(),
          )
        : NotificationListener<ScrollUpdateNotification>(
            onNotification: (notification) {
              if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
                // _handlePagination();
                // Handle video pagination
                if (!state.getUserByIdTextPostLoadingMore) {
                  _isLoadingMore = true;
                  context.read<HomeFeedBloc>().add(
                      GetUserByIdTextPostApiIdEvent(page: state.getUserByIdTextPostPage + 1, userId: widget.userId));
                }
              }
              return true;
            },
            child: ListView.builder(
              physics: NeverScrollableScrollPhysics(),
              padding: EdgeInsets.only(top: 10.h, bottom: 55.h),
              itemCount: state.getUserByIdTextPostData.length,
              itemBuilder: (context, index) {
                final textPost = state.getUserByIdTextPostData[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: 0.h),
                  child: RepaintBoundary(
                    child: PostWidget(
                      key: ValueKey('text_post_${textPost.id}'),
                      width: 0,
                      height: 0,
                      userByIDpost: false,
                      userByIDvideo: false,
                      userVideo: false,
                      userpost: true,
                      isPost: true,
                      isTextPost: true,
                      userById: true,
                      state: state,
                      index: index,
                      userId: textPost.user?.userId ?? 0,
                      latestcomments: textPost.latestComment?.toString() ?? '',
                      postId: textPost.id ?? 0,
                      profileImage: textPost.user?.profileImage ?? '',
                      name: textPost.user?.name ?? '',
                      username: textPost.user?.username ?? '',
                      postMedia: [],
                      thumbnailImage: [],
                      title: textPost.title ?? '',
                      caption:
                          "${textPost.title == "''" || (textPost.title?.isEmpty ?? true) ? '' : textPost.title}${(textPost.description?.isEmpty ?? true) ? '' : textPost.title == "''" || (textPost.title?.isEmpty ?? true) ? textPost.description : "\n${textPost.description}"}",
                      likes: textPost.likes?.toString() ?? '0',
                      comments: textPost.commentsCount?.toString() ?? '0',
                      postTime: textPost.createdAt ?? '',
                      isLiked: textPost.isLiked ?? false,
                      isSaved: textPost.isSaved ?? false,
                      taggedIn: textPost.taggedIn,
                      doubleTap: () {
                        if (textPost.isLiked == false) {
                          context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: textPost.id ?? 0));
                        }
                      },
                      likeonTap: () {
                        context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: textPost.id ?? 0));
                      },
                      commentonTap: () {
                        showModalBottomSheet(
                          context: context,
                          useRootNavigator: true,
                          isScrollControlled: true,
                          builder: (context) => CommentsBottomSheet(postId: textPost.id ?? 0),
                        );
                      },
                      shareonTap: () {},
                      saveonTap: () {},
                    ),
                  ),
                );
              },
            ),
          );
  }

  Widget _buildTagPeopleContent(HomeFeedState state) {
    return SingleChildScrollView(
      child: Column(
        children: [
          buildSizedBoxH(20.0),
          ExceptionWidget(
            imagePath: Assets.images.pngs.other.pngComingSoon.path,
            title: Lang.of(context).lbl_coming_soon_title,
            subtitle: Lang.of(context).lbl_coming_soon_subtitle,
            showButton: false,
          ),
        ],
      ),
    );
  }

  Widget _buildFollowButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      child: SizedBox(
        width: double.infinity,
        child: CustomElevatedButton(
          margin: EdgeInsets.only(bottom: 10.h),
          color: Theme.of(context).primaryColor,
          decoration: const BoxDecoration(
            boxShadow: [
              BoxShadow(
                blurRadius: 8,
                color: Colors.black12,
                offset: Offset(0, 0),
              ),
            ],
          ),
          text: Lang.of(context).lbl_follow,
          buttonTextStyle: Theme.of(context).textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 16.0.sp,
                color: Theme.of(context).customColors.white,
              ),
          height: 40.h,
        ),
      ),
    );
  }

  Widget _buildUnBlockedbutton() {
    return InkWell(
      onTap: () {
        context
            .read<HomeFeedBloc>()
            .add(BlockUserApiEvent(userId: widget.userId.toString(), context: context, isblocked: true));
        VibrationHelper.singleShortBuzz();
        Future.delayed(
          const Duration(milliseconds: 1500),
          () {
            _refreshFeed();
          },
        );
      },
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          border: Border.all(color: Theme.of(context).primaryColor),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          child: Text(
            Lang.of(context).lbl_unblock,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 12.sp,
                  color: Theme.of(context).customColors.white,
                ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  void _showBlockBottomSheet(BuildContext context) {
    final more = [
      ReportReason(
        reason: Lang.of(context).lbl_block,
        onTap: () {
          NavigatorService.goBack();
          showDialog(
            context: context,
            builder: (ctx) {
              bool isLoading = false;
              return StatefulBuilder(builder: (ctx, setState) {
                return CustomAlertDialog(
                    imagePath: Assets.images.pngs.other.pngBlock.path,
                    imageheight: 30.h,
                    imagewidth: 30.w,
                    title: "Block User?",
                    subtitle: Lang.of(ctx).msg_block_user_title,
                    onConfirmButtonPressed: () async {
                      setState(() {
                        isLoading = true;
                      });
                      context
                          .read<HomeFeedBloc>()
                          .add(BlockUserApiEvent(userId: widget.userId.toString(), context: context, isblocked: false));
                      _refreshFeed();
                      VibrationHelper.singleShortBuzz();
                      Navigator.pop(context);
                    },
                    confirmButtonText: Lang.of(ctx).lbl_confirm,
                    isLoading: isLoading);
              });
            },
          );
        },
      ),
    ];

    showModalBottomSheet(
      useRootNavigator: true,
      isDismissible: true,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return CommonBottomSheet(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 8.0),
          child: StatefulBuilder(
            builder: (context, setState) => ScrollConfiguration(
              behavior: MyBehavior(),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    buildSizedBoxH(10),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemCount: more.length,
                      itemBuilder: (context, index) {
                        final reason = more[index];
                        return _buildMoreBottomsheetItem(
                          label: reason.reason,
                          onTap: reason.onTap,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMoreBottomsheetItem({required String label, required VoidCallback onTap}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(30.r),
      ),
      child: ListTile(
        //  minTileHeight: 60.h,
        onTap: onTap,
        leading: CustomImageView(
          height: 20.h,
          width: 20.w,
          imagePath: Assets.images.svg.setting.svgBlockedPeople.path,
          fit: BoxFit.contain,
        ),
        title: Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
        ),
      ),
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: 20.h,
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ReportReason {
  final String reason;
  final VoidCallback onTap;

  ReportReason({required this.reason, required this.onTap});
}

class BioTextWidget extends StatefulWidget {
  final String text;

  const BioTextWidget({super.key, required this.text});

  @override
  State<BioTextWidget> createState() => _BioTextWidgetState();
}

class _BioTextWidgetState extends State<BioTextWidget> {
  bool _expanded = false;
  bool _hasOverflow = false;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final textSpan = TextSpan(
        text: widget.text,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp),
      );

      final textPainter = TextPainter(
        text: textSpan,
        maxLines: 5,
        textDirection: TextDirection.ltr,
      )..layout(maxWidth: constraints.maxWidth);

      _hasOverflow = textPainter.didExceedMaxLines;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.text,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontSize: 16.sp),
            maxLines: _expanded ? null : 5,
            overflow: _expanded ? TextOverflow.visible : TextOverflow.ellipsis,
          ),
          if (_hasOverflow)
            GestureDetector(
              onTap: () {
                setState(() {
                  _expanded = !_expanded;
                });
              },
              child: Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  _expanded ? 'See less' : 'See more',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ),
            ),
        ],
      );
    });
  }
}
