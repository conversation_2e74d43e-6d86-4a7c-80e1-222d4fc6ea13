LiveUserModel deserializeLiveUserModel(Map<String, dynamic> json) => LiveUserModel.fromJson(json);

class LiveUserModel {
  bool? status;
  String? message;
  List<LiveNewStory>? data;

  LiveUserModel({this.status, this.message, this.data});

  LiveUserModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['data'] != null) {
      data = <LiveNewStory>[];
      json['data'].forEach((v) {
        data!.add(LiveNewStory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // CopyWith method
  LiveUserModel copyWith({
    bool? status,
    String? message,
    List<LiveNewStory>? data,
  }) {
    return LiveUserModel(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }
}

class LiveNewStory {
  int? userId;
  String? username;
  String? userprofile;
  List<LiveStories>? stories;

  LiveNewStory({this.userId, this.username, this.userprofile, this.stories});

  LiveNewStory.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    username = json['username'];
    userprofile = json['userprofile'];
    if (json['stories'] != null) {
      stories = <LiveStories>[];
      json['stories'].forEach((v) {
        stories!.add(LiveStories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['username'] = username;
    data['userprofile'] = userprofile;
    if (stories != null) {
      data['stories'] = stories!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // CopyWith method
  LiveNewStory copyWith({
    int? userId,
    String? username,
    String? userprofile,
    List<LiveStories>? stories,
  }) {
    return LiveNewStory(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      userprofile: userprofile ?? this.userprofile,
      stories: stories ?? this.stories,
    );
  }
}

class LiveStories {
  int? storyId;
  String? title;
  String? storytype;
  String? storyfile;
  bool? isLiked;

  LiveStories({this.storyId, this.title, this.storytype, this.storyfile, this.isLiked});

  LiveStories.fromJson(Map<String, dynamic> json) {
    storyId = json['story_id'];
    title = json['title'];
    storytype = json['storytype'];
    storyfile = json['storyfile'];
    isLiked = json['is_liked'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['story_id'] = storyId;
    data['title'] = title;
    data['storytype'] = storytype;
    data['storyfile'] = storyfile;
    data['is_liked'] = isLiked;
    return data;
  }

  // CopyWith method
  LiveStories copyWith({
    int? storyId,
    String? title,
    String? storytype,
    String? storyfile,
    bool? isLiked,
  }) {
    return LiveStories(
      storyId: storyId ?? this.storyId,
      title: title ?? this.title,
      storytype: storytype ?? this.storytype,
      storyfile: storyfile ?? this.storyfile,
      isLiked: isLiked ?? this.isLiked,
    );
  }
}
